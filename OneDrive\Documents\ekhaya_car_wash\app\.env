# Database Configuration - PostgreSQL
DATABASE_URL="postgresql://postgres:postgres2@localhost/Ekhaya_car_wash"

# NextAuth.js Configuration
NEXTAUTH_SECRET="ekhaya-car-wash-secret-key-2024"
NEXTAUTH_URL="http://localhost:3000"

# Optional: Stripe (for payments - can be added later)
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=""
# STRIPE_SECRET_KEY=""
# STRIPE_WEBHOOK_SECRET=""

# Example for local PostgreSQL with different credentials:
# DATABASE_URL="postgresql://postgres:yourpassword@localhost:5432/Ekhaya_car_wash"

# Production Environment Variables (uncomment and update for production)
# DATABASE_URL="********************************************************/Ekhaya_car_wash"
# STRIPE_PUBLISHABLE_KEY="pk_live_your_live_publishable_key"
# STRIPE_SECRET_KEY="sk_live_your_live_secret_key"
# NEXTAUTH_URL="https://your-production-domain.com"