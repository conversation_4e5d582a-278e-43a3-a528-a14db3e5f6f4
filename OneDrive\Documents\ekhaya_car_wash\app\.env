# Database Configuration - PostgreSQL
DATABASE_URL="postgresql://postgres:postgres2@localhost/Ekhaya_car_wash"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="oJ83VxajhT8DymOYPCF0kB7DKgccM7HC"

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key_here"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key_here"
STRIPE_WEBHOOK_SECRET="whsec_your_stripe_webhook_secret_here"

# Stripe Client-side (Next.js public env vars)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key_here"

# Example for local PostgreSQL with different credentials:
# DATABASE_URL="postgresql://postgres:yourpassword@localhost:5432/Ekhaya_car_wash"

# Production Environment Variables (uncomment and update for production)
# DATABASE_URL="********************************************************/Ekhaya_car_wash"
# STRIPE_PUBLISHABLE_KEY="pk_live_your_live_publishable_key"
# STRIPE_SECRET_KEY="sk_live_your_live_secret_key"
# NEXTAUTH_URL="https://your-production-domain.com"