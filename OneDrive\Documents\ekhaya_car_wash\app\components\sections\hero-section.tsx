
'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Car, Shield, Sparkles, Star, Users, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import Autoplay from 'embla-carousel-autoplay';

const carouselSlides = [
  {
    id: 1,
    title: "Premium Car Care Experience",
    description: "Relax in our productive lounge while we take care of your vehicle with professional service and attention to detail.",
    icon: Sparkles,
    stats: { rating: "4.9", customers: "2000+", services: "Express, Premium, Deluxe, Executive" }
  },
  {
    id: 2,
    title: "Eco-Friendly & Certified",
    description: "Our eco-friendly cleaning solutions and water recycling systems ensure premium care for your vehicle and the environment.",
    icon: Shield,
    stats: { rating: "4.8", customers: "1500+", services: "Green Technology Certified" }
  },
  {
    id: 3,
    title: "Professional Service Team",
    description: "Our trained specialists use premium products and techniques to deliver exceptional results every time.",
    icon: Users,
    stats: { rating: "4.9", customers: "3000+", services: "Expert Detailing Available" }
  },
  {
    id: 4,
    title: "Quality Guaranteed",
    description: "100% satisfaction guarantee on all our services. Your vehicle gets the care it deserves with professional excellence.",
    icon: CheckCircle,
    stats: { rating: "5.0", customers: "1800+", services: "Satisfaction Guaranteed" }
  }
];

export function HeroSection() {
  const [emblaRef, emblaApi] = useEmblaCarousel(
    { 
      loop: true,
      align: 'start',
      skipSnaps: false,
      dragFree: false
    },
    [Autoplay({ delay: 4000, stopOnInteraction: false })]
  );

  const [selectedIndex, setSelectedIndex] = useState(0);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on('select', onSelect);
  }, [emblaApi, onSelect]);

  return (
    <section className="relative ekhaya-gradient py-20 md:py-28">
      <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center lg:text-left"
          >
            {/* Eco Badge */}
            <div className="flex justify-center lg:justify-start mb-6">
              <Badge className="bg-green-100 text-green-700 border-green-200 px-4 py-2 text-sm font-medium">
                <Shield className="w-4 h-4 mr-2" />
                ECO CERTIFIED 2024
              </Badge>
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              EkhayaIntel Car Wash –<br />
              <span className="text-yellow-300">Where Waiting Becomes Productive</span><br />
              <span className="text-blue-100 text-2xl md:text-3xl lg:text-4xl">in Cape Town</span>
            </h1>

            {/* Subtitle */}
            <p className="text-lg md:text-xl text-blue-100 mb-8 max-w-2xl mx-auto lg:mx-0">
              Premium car care with productive customer lounge experience
            </p>

            {/* CTA Button */}
            <Link href="/book">
              <Button size="lg" className="bg-white text-red-600 hover:bg-red-50 px-8 py-3 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
                <Car className="w-5 h-5 mr-2" />
                Book Your Service Now
              </Button>
            </Link>
          </motion.div>

          {/* Carousel Section */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 overflow-hidden">
              {/* Animated Logo/Car Section at top */}
              <div className="flex justify-center p-6 pb-4">
                <motion.div 
                  className="relative car-entrance"
                  initial={{ x: -200, opacity: 0, scale: 0.8 }}
                  animate={{ x: 0, opacity: 1, scale: 1 }}
                  transition={{ 
                    duration: 3,
                    ease: "easeOut",
                    delay: 0.5
                  }}
                >
                  <div className="relative h-16 w-24 bg-white rounded-lg p-1 shadow-lg">
                    <Image
                      src="/logocarwash.jpg"
                      alt="Ekhaya Car Wash"
                      fill
                      className="object-contain rounded-md"
                    />
                  </div>
                  {/* Car trail effect */}
                  <motion.div
                    className="absolute -top-1 -left-4 h-2 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: "150px" }}
                    transition={{ duration: 1.5, delay: 0.8 }}
                  />
                </motion.div>
              </div>

              {/* Carousel Container */}
              <div className="overflow-hidden" ref={emblaRef}>
                <div className="flex">
                  {carouselSlides.map((slide, index) => (
                    <div key={slide.id} className="flex-[0_0_100%] px-8 pb-8">
                      <motion.div 
                        className="text-center"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 2.0 + (index * 0.1) }}
                      >
                        {/* Icon */}
                        <div className="flex justify-center mb-4">
                          <div className="bg-white/20 rounded-full p-3">
                            <slide.icon className="w-8 h-8 text-white" />
                          </div>
                        </div>

                        {/* Title */}
                        <h3 className="text-xl font-bold text-white mb-3">
                          {slide.title}
                        </h3>
                        
                        {/* Description */}
                        <p className="text-blue-100 text-sm mb-4 leading-relaxed">
                          {slide.description}
                        </p>

                        {/* Stats */}
                        <div className="flex justify-center items-center space-x-4 text-xs">
                          <div className="flex items-center">
                            <Star className="w-3 h-3 text-yellow-300 mr-1" />
                            <span className="text-white font-semibold">{slide.stats.rating}</span>
                          </div>
                          <div className="text-blue-200">
                            {slide.stats.customers} customers
                          </div>
                        </div>
                        
                        <div className="mt-2 text-xs text-blue-300">
                          {slide.stats.services}
                        </div>
                      </motion.div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Carousel Dots */}
              <div className="flex justify-center space-x-2 pb-6">
                {carouselSlides.map((_, index) => (
                  <button
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === selectedIndex ? 'bg-white' : 'bg-white/40'
                    }`}
                    onClick={() => emblaApi?.scrollTo(index)}
                  />
                ))}
              </div>

              {/* Floating sparkles animation */}
              <div className="absolute top-4 right-4">
                <motion.div
                  animate={{ 
                    rotate: 360,
                    scale: [1, 1.2, 1]
                  }}
                  transition={{ 
                    rotate: { duration: 4, repeat: Infinity, ease: "linear" },
                    scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  <Sparkles className="w-4 h-4 text-yellow-300" />
                </motion.div>
              </div>
              
              <div className="absolute bottom-4 left-4">
                <motion.div
                  animate={{ 
                    rotate: -360,
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ 
                    rotate: { duration: 5, repeat: Infinity, ease: "linear" },
                    scale: { duration: 3, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  <Sparkles className="w-3 h-3 text-blue-300" />
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
