generator client {
    provider = "prisma-client-js"
    binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
    output = "/home/<USER>/ekhaya_car_wash/app/node_modules/.prisma/client"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

// NextAuth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Core Application Models
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  password      String?
  name          String?
  firstName     String?
  lastName      String?
  phone         String?
  dateOfBirth   DateTime?
  gender        String?
  address       String?
  city          String?
  province      String?
  language      String?   @default("English")
  profileImage  String?
  isAdmin       Boolean   @default(false)
  loyaltyPoints Int       @default(0)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts         Account[]
  sessions         Session[]
  vehicles         Vehicle[]
  bookings         Booking[]
  paymentMethods   PaymentMethod[]
  membership       Membership?
  reviews          Review[]
  notifications    Notification[]
}

model Vehicle {
  id           String @id @default(cuid())
  userId       String
  make         String
  model        String
  year         Int
  color        String
  licensePlate String
  vehicleType  String // Sedan, SUV, Hatchback, Bakkie
  isPrimary    Boolean @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  bookings Booking[]
}

model Service {
  id          String @id @default(cuid())
  name        String @unique
  description String
  shortDesc   String
  price       Int    // Price in cents (R80 = 8000)
  duration    Int    // Duration in minutes
  category    String // Express, Premium, Deluxe, Executive
  features    String[] // Array of features
  isActive    Boolean @default(true)
  rating      Float   @default(0)
  reviewCount Int     @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  bookings    Booking[]
  addOns      ServiceAddOn[]
  reviews     Review[]
}

model ServiceAddOn {
  id          String @id @default(cuid())
  serviceId   String
  name        String
  description String
  price       Int    // Price in cents
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())

  service        Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  bookingAddOns  BookingAddOn[]
}

model Booking {
  id            String    @id @default(cuid())
  userId        String
  serviceId     String
  vehicleId     String
  bookingDate   DateTime
  timeSlot      String
  status        String    @default("confirmed") // confirmed, completed, cancelled, in-progress
  totalAmount   Int       // Total in cents
  baseAmount    Int       // Service base price
  addOnAmount   Int       @default(0)
  notes         String?
  cancellationReason String?
  cancelledAt   DateTime?
  completedAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  user       User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  service    Service        @relation(fields: [serviceId], references: [id])
  vehicle    Vehicle        @relation(fields: [vehicleId], references: [id])
  addOns     BookingAddOn[]
  payment    Payment?
}

model BookingAddOn {
  id          String @id @default(cuid())
  bookingId   String
  addOnId     String
  quantity    Int    @default(1)
  price       Int    // Price at time of booking

  booking Booking      @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  addOn   ServiceAddOn @relation(fields: [addOnId], references: [id])
}

model PaymentMethod {
  id           String  @id @default(cuid())
  userId       String
  type         String  // visa, mastercard, etc.
  lastFour     String
  expiryMonth  Int
  expiryYear   Int
  cardholderName String
  isDefault    Boolean @default(false)
  isActive     Boolean @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments Payment[]
}

model Payment {
  id              String    @id @default(cuid())
  bookingId       String    @unique
  paymentMethodId String?
  amount          Int       // Amount in cents
  status          String    @default("pending") // pending, completed, failed, refunded
  transactionId   String?
  paymentDate     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  booking       Booking        @relation(fields: [bookingId], references: [id])
  paymentMethod PaymentMethod? @relation(fields: [paymentMethodId], references: [id])
}

model Membership {
  id         String   @id @default(cuid())
  userId     String   @unique
  plan       String   // Basic, Premium, Elite
  price      Int      // Monthly price in cents
  startDate  DateTime
  endDate    DateTime?
  isActive   Boolean  @default(true)
  autoRenew  Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Review {
  id        String   @id @default(cuid())
  userId    String
  serviceId String
  rating    Int      // 1-5 stars
  comment   String?
  isVisible Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  title     String
  message   String
  type      String   // booking, promotion, reminder, system
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
